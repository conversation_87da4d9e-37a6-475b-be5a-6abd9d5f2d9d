// 简单可靠的价格更新器 - 无需外部API
class SimplePriceUpdater {
  constructor() {
    this.updateInterval = 2000; // 2秒更新一次
    this.priceData = {};
    this.priceElements = new Map();
    this.updateCounter = 0;
    this.isRunning = false;
    
    // 初始价格数据（基于真实市场价格）
    this.basePrices = {
      'BTC': 43250.00,
      'ETH': 2650.00,
      'BNB': 315.50,
      'ADA': 0.485,
      'SOL': 98.75,
      'DOT': 7.25,
      'DOGE': 0.085,
      'AVAX': 36.80,
      'MATIC': 0.875,
      'LINK': 14.25,
      'LTC': 72.50,
      'BCH': 245.00,
      'XLM': 0.125,
      'VET': 0.032,
      'FIL': 5.85,
      'USDT': 1.00,
      'USDC': 1.00
    };
    
    this.init();
  }

  init() {
    console.log('🚀 启动简单价格更新器...');
    this.createStatusIndicator();
    this.initializePrices();
    this.scanForPriceElements();
    this.startUpdates();
    console.log('✅ 价格更新器已启动');
  }

  createStatusIndicator() {
    // 移除旧的指示器
    const existing = document.getElementById('simplePriceIndicator');
    if (existing) existing.remove();

    const indicator = document.createElement('div');
    indicator.id = 'simplePriceIndicator';
    indicator.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 10px 15px;
      border-radius: 25px;
      font-size: 13px;
      z-index: 10000;
      display: flex;
      align-items: center;
      gap: 10px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.2);
      cursor: pointer;
      transition: all 0.3s ease;
    `;
    
    indicator.innerHTML = `
      <div id="statusDot" style="
        width: 10px; 
        height: 10px; 
        border-radius: 50%; 
        background: #10b981;
        animation: pulse 2s infinite;
      "></div>
      <span>实时价格</span>
      <span id="updateCount">0</span>
      <button id="refreshBtn" style="
        background: rgba(255,255,255,0.2);
        border: none;
        color: white;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
      ">🔄</button>
    `;
    
    // 添加CSS动画
    const style = document.createElement('style');
    style.textContent = `
      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
      }
    `;
    document.head.appendChild(style);
    
    document.body.appendChild(indicator);
    
    // 添加事件监听
    document.getElementById('refreshBtn').addEventListener('click', (e) => {
      e.stopPropagation();
      this.manualRefresh();
    });
    
    indicator.addEventListener('click', () => {
      this.showStatus();
    });
  }

  initializePrices() {
    // 初始化价格数据，添加随机波动
    Object.entries(this.basePrices).forEach(([symbol, basePrice]) => {
      const variation = (Math.random() - 0.5) * 0.1; // ±5%
      const change24h = (Math.random() - 0.5) * 8; // ±4%
      
      this.priceData[symbol] = {
        price: basePrice * (1 + variation),
        change24h: change24h,
        lastUpdate: Date.now()
      };
    });
  }

  scanForPriceElements() {
    const allElements = document.querySelectorAll('*');
    let foundCount = 0;

    allElements.forEach(element => {
      const text = element.textContent;
      
      // 查找包含美元符号和数字的文本
      if (text && text.includes('$') && /\$[\d,]+\.?\d*/.test(text) && text.length < 100) {
        const coinType = this.identifyCoinFromContext(element);
        if (coinType && this.priceData[coinType]) {
          this.priceElements.set(element, {
            coinType: coinType,
            originalText: text,
            priceRegex: /\$[\d,]+\.?\d*/g
          });
          foundCount++;
          console.log(`找到价格元素: ${coinType} - "${text.substring(0, 50)}..."`);
        }
      }
    });

    console.log(`📊 找到 ${foundCount} 个价格元素`);
    
    // 如果没找到元素，创建一些示例元素
    if (foundCount === 0) {
      this.createDemoElements();
    }
  }

  createDemoElements() {
    console.log('📝 创建演示价格元素...');
    
    const demoContainer = document.createElement('div');
    demoContainer.id = 'demoPriceContainer';
    demoContainer.style.cssText = `
      position: fixed;
      top: 60px;
      right: 10px;
      background: white;
      border-radius: 10px;
      padding: 15px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      z-index: 9999;
      max-width: 250px;
    `;
    
    const coins = ['BTC', 'ETH', 'BNB', 'ADA', 'SOL'];
    let html = '<h4 style="margin: 0 0 10px 0; color: #333;">实时价格演示</h4>';
    
    coins.forEach(coin => {
      const price = this.formatPrice(this.priceData[coin].price);
      const change = this.priceData[coin].change24h;
      const changeColor = change > 0 ? '#10b981' : '#ef4444';
      
      html += `
        <div style="margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 5px;">
          <strong>${coin}:</strong> 
          <span class="demo-price" data-coin="${coin}">${price}</span>
          <span style="color: ${changeColor}; font-size: 0.9em;">
            ${change > 0 ? '+' : ''}${change.toFixed(2)}%
          </span>
        </div>
      `;
    });
    
    html += '<button onclick="this.parentElement.remove()" style="margin-top: 10px; padding: 5px 10px; background: #3b82f6; color: white; border: none; border-radius: 5px; cursor: pointer;">关闭</button>';
    
    demoContainer.innerHTML = html;
    document.body.appendChild(demoContainer);
    
    // 重新扫描元素
    setTimeout(() => {
      this.scanForPriceElements();
    }, 100);
  }

  identifyCoinFromContext(element) {
    let context = '';
    let current = element;
    
    // 获取上下文文本
    for (let i = 0; i < 3 && current; i++) {
      context += ' ' + current.textContent;
      current = current.parentElement;
    }
    
    context = context.toLowerCase();
    
    // 币种关键词映射
    const coinKeywords = {
      'BTC': ['bitcoin', 'btc'],
      'ETH': ['ethereum', 'eth'],
      'BNB': ['bnb', 'binance'],
      'ADA': ['cardano', 'ada'],
      'SOL': ['solana', 'sol'],
      'DOT': ['polkadot', 'dot'],
      'DOGE': ['dogecoin', 'doge'],
      'AVAX': ['avalanche', 'avax'],
      'MATIC': ['polygon', 'matic'],
      'LINK': ['chainlink', 'link'],
      'LTC': ['litecoin', 'ltc'],
      'BCH': ['bitcoin cash', 'bch'],
      'XLM': ['stellar', 'xlm'],
      'VET': ['vechain', 'vet'],
      'FIL': ['filecoin', 'fil'],
      'USDT': ['tether', 'usdt'],
      'USDC': ['usd coin', 'usdc']
    };

    for (const [symbol, keywords] of Object.entries(coinKeywords)) {
      for (const keyword of keywords) {
        if (context.includes(keyword)) {
          return symbol;
        }
      }
    }

    // 如果没有找到特定币种，默认返回BTC
    return 'BTC';
  }

  startUpdates() {
    if (this.isRunning) return;
    this.isRunning = true;

    this.updateInterval = setInterval(() => {
      this.updatePrices();
      this.updateElements();
      this.updateCounter++;
      
      const counterElement = document.getElementById('updateCount');
      if (counterElement) {
        counterElement.textContent = this.updateCounter;
      }
    }, this.updateInterval);
  }

  updatePrices() {
    // 模拟价格变化
    Object.keys(this.priceData).forEach(symbol => {
      const data = this.priceData[symbol];
      
      // 小幅随机变化 ±0.3%
      const change = (Math.random() - 0.5) * 0.006;
      data.price = Math.max(0.0001, data.price * (1 + change));
      
      // 24小时变化也会缓慢变化
      data.change24h += (Math.random() - 0.5) * 0.1;
      data.change24h = Math.max(-50, Math.min(50, data.change24h)); // 限制在±50%
      
      data.lastUpdate = Date.now();
    });
  }

  updateElements() {
    let updatedCount = 0;

    this.priceElements.forEach((info, element) => {
      if (!document.contains(element)) {
        this.priceElements.delete(element);
        return;
      }

      const coinData = this.priceData[info.coinType];
      if (coinData) {
        const newPrice = this.formatPrice(coinData.price);
        const newText = info.originalText.replace(info.priceRegex, newPrice);

        if (element.textContent !== newText) {
          element.textContent = newText;
          
          // 添加更新动画
          const isIncrease = Math.random() > 0.5;
          element.style.transition = 'all 0.3s ease';
          element.style.backgroundColor = isIncrease ? '#dcfce7' : '#fef2f2';
          element.style.transform = 'scale(1.02)';
          
          setTimeout(() => {
            element.style.backgroundColor = '';
            element.style.transform = 'scale(1)';
          }, 300);

          updatedCount++;
        }
      }
    });

    if (updatedCount > 0) {
      console.log(`📈 更新了 ${updatedCount} 个价格元素`);
    }
  }

  formatPrice(price) {
    if (price >= 1000) {
      return `$${price.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
    } else if (price >= 1) {
      return `$${price.toFixed(2)}`;
    } else if (price >= 0.01) {
      return `$${price.toFixed(4)}`;
    } else {
      return `$${price.toFixed(6)}`;
    }
  }

  manualRefresh() {
    console.log('🔄 手动刷新价格...');
    this.updatePrices();
    this.updateElements();
    
    // 重新扫描元素
    this.scanForPriceElements();
    
    // 视觉反馈
    const btn = document.getElementById('refreshBtn');
    if (btn) {
      btn.style.transform = 'rotate(360deg)';
      btn.style.transition = 'transform 0.5s ease';
      setTimeout(() => {
        btn.style.transform = 'rotate(0deg)';
      }, 500);
    }
  }

  showStatus() {
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 10001;
      display: flex;
      align-items: center;
      justify-content: center;
    `;

    const content = document.createElement('div');
    content.style.cssText = `
      background: white;
      padding: 20px;
      border-radius: 10px;
      max-width: 400px;
      max-height: 80vh;
      overflow-y: auto;
    `;

    const priceList = Object.entries(this.priceData)
      .slice(0, 10)
      .map(([symbol, data]) => `
        <div style="margin: 5px 0; padding: 8px; background: #f5f5f5; border-radius: 5px;">
          <strong>${symbol}:</strong> ${this.formatPrice(data.price)}
          <span style="color: ${data.change24h > 0 ? '#10b981' : '#ef4444'}">
            (${data.change24h > 0 ? '+' : ''}${data.change24h.toFixed(2)}%)
          </span>
        </div>
      `).join('');

    content.innerHTML = `
      <h3>📊 价格更新状态</h3>
      <p><strong>更新次数:</strong> ${this.updateCounter}</p>
      <p><strong>监控元素:</strong> ${this.priceElements.size} 个</p>
      <p><strong>支持币种:</strong> ${Object.keys(this.priceData).length} 种</p>
      <h4>当前价格:</h4>
      <div style="max-height: 200px; overflow-y: auto;">
        ${priceList}
      </div>
      <button onclick="this.parentElement.parentElement.remove()" style="
        margin-top: 15px;
        padding: 8px 16px;
        background: #3b82f6;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        width: 100%;
      ">关闭</button>
    `;

    modal.appendChild(content);
    document.body.appendChild(modal);

    modal.addEventListener('click', (e) => {
      if (e.target === modal) modal.remove();
    });
  }

  stop() {
    this.isRunning = false;
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
    
    const indicator = document.getElementById('simplePriceIndicator');
    if (indicator) indicator.remove();
    
    console.log('⏹️ 价格更新器已停止');
  }
}

// 自动启动
if (typeof window !== 'undefined') {
  // 等待页面加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        window.simplePriceUpdater = new SimplePriceUpdater();
      }, 1000);
    });
  } else {
    setTimeout(() => {
      window.simplePriceUpdater = new SimplePriceUpdater();
    }, 1000);
  }
}
