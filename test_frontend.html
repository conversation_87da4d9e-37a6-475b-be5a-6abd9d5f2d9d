<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端连接测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #3b82f6;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #10b981;
        }
        
        .error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #ef4444;
        }
        
        .loading {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #f59e0b;
        }
        
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #2563eb;
        }
        
        .price-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .price-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .price-symbol {
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }
        
        .price-value {
            font-size: 1.2em;
            color: #3182ce;
            font-weight: bold;
        }
        
        .price-change {
            font-size: 0.9em;
            margin-top: 5px;
        }
        
        .positive { color: #10b981; }
        .negative { color: #ef4444; }
        
        #log {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 前端连接测试</h1>
        
        <div class="test-section">
            <h3>📡 连接状态</h3>
            <div id="connectionStatus" class="status loading">正在测试连接...</div>
            
            <button class="btn" onclick="testConnection()">🔄 重新测试</button>
            <button class="btn" onclick="fetchPrices()">📊 获取价格</button>
            <button class="btn" onclick="clearLog()">🗑️ 清空日志</button>
        </div>
        
        <div class="test-section">
            <h3>💰 实时价格数据</h3>
            <div id="priceContainer">
                <p>点击"获取价格"按钮来加载数据</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📋 测试日志</h3>
            <div id="log"></div>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:8080/api/prices';
        const HEALTH_URL = 'http://localhost:8080/health';
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        async function testConnection() {
            log('🔍 开始测试连接...');
            updateStatus('正在测试连接...', 'loading');
            
            try {
                // 测试健康检查
                log('📡 测试健康检查接口...');
                const healthResponse = await fetch(HEALTH_URL);
                
                if (healthResponse.ok) {
                    const healthData = await healthResponse.json();
                    log(`✅ 健康检查成功: ${JSON.stringify(healthData)}`);
                    
                    // 测试价格接口
                    log('📊 测试价格数据接口...');
                    const priceResponse = await fetch(API_URL);
                    
                    if (priceResponse.ok) {
                        const priceData = await priceResponse.json();
                        log(`✅ 价格数据获取成功，币种数量: ${priceData.count}`);
                        updateStatus('✅ 连接成功！后端正常工作', 'success');
                        return true;
                    } else {
                        throw new Error(`价格接口错误: ${priceResponse.status}`);
                    }
                } else {
                    throw new Error(`健康检查失败: ${healthResponse.status}`);
                }
                
            } catch (error) {
                log(`❌ 连接测试失败: ${error.message}`);
                updateStatus('❌ 连接失败！请检查后端是否启动', 'error');
                
                if (error.message.includes('Failed to fetch')) {
                    log('💡 提示: 请确保Python后端已启动 (python3 crypto_price_server.py)');
                }
                
                return false;
            }
        }
        
        async function fetchPrices() {
            log('📊 获取价格数据...');
            
            try {
                const response = await fetch(API_URL);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ 成功获取 ${data.count} 个币种的价格数据`);
                    displayPrices(data.data);
                } else {
                    throw new Error('API返回失败状态');
                }
                
            } catch (error) {
                log(`❌ 获取价格失败: ${error.message}`);
                document.getElementById('priceContainer').innerHTML = 
                    `<p style="color: #dc2626;">获取价格失败: ${error.message}</p>`;
            }
        }
        
        function displayPrices(priceData) {
            const container = document.getElementById('priceContainer');
            
            if (!priceData || Object.keys(priceData).length === 0) {
                container.innerHTML = '<p>没有价格数据</p>';
                return;
            }
            
            let html = '<div class="price-grid">';
            
            Object.entries(priceData).forEach(([symbol, data]) => {
                const price = data.price || 0;
                const change = data.change_24h || 0;
                const source = data.source || 'unknown';
                
                const changeClass = change > 0 ? 'positive' : change < 0 ? 'negative' : '';
                const changeText = change > 0 ? `+${change.toFixed(2)}%` : `${change.toFixed(2)}%`;
                
                html += `
                    <div class="price-item">
                        <div class="price-symbol">${symbol}</div>
                        <div class="price-value">$${price.toLocaleString('en-US', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 4
                        })}</div>
                        <div class="price-change ${changeClass}">${changeText}</div>
                        <div style="font-size: 0.8em; color: #6b7280; margin-top: 5px;">[${source}]</div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
            
            log(`📈 显示了 ${Object.keys(priceData).length} 个币种的价格`);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 页面加载时自动测试连接
        window.addEventListener('load', () => {
            log('🚀 页面加载完成，开始自动测试...');
            testConnection();
        });
        
        // 每30秒自动刷新价格
        setInterval(() => {
            if (document.getElementById('connectionStatus').classList.contains('success')) {
                fetchPrices();
            }
        }, 30000);
    </script>
</body>
</html>
