<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时价格更新测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #3b82f6;
        }
        
        .crypto-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .crypto-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .crypto-name {
            font-weight: bold;
            color: #2d3748;
        }
        
        .crypto-price {
            font-size: 1.2em;
            font-weight: bold;
            color: #3182ce;
        }
        
        .crypto-change {
            font-size: 0.9em;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .positive {
            background: #dcfce7;
            color: #166534;
        }
        
        .negative {
            background: #fef2f2;
            color: #dc2626;
        }
        
        .status-info {
            background: #e0f2fe;
            border: 1px solid #0284c7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 14px;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #2563eb;
        }
        
        .btn:active {
            transform: scale(0.98);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px;
            }
            
            .crypto-item {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 实时价格更新测试页面</h1>
        
        <div class="status-info">
            <h3>📊 系统状态</h3>
            <p>这个页面用于测试新的简单可靠价格更新系统。系统会自动识别页面中的价格元素并实时更新。</p>
            <p><strong>特点：</strong> 无需外部API，使用模拟数据，100%可靠运行</p>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="manualRefresh()">🔄 手动刷新</button>
            <button class="btn" onclick="addMoreElements()">➕ 添加更多元素</button>
            <button class="btn" onclick="showSystemInfo()">ℹ️ 系统信息</button>
        </div>
        
        <div class="test-section">
            <h3>💰 主要加密货币价格</h3>
            
            <div class="crypto-item">
                <div class="crypto-name">Bitcoin (BTC)</div>
                <div class="crypto-price">$43,250.00</div>
                <div class="crypto-change positive">+2.34%</div>
            </div>
            
            <div class="crypto-item">
                <div class="crypto-name">Ethereum (ETH)</div>
                <div class="crypto-price">$2,650.00</div>
                <div class="crypto-change negative">-1.23%</div>
            </div>
            
            <div class="crypto-item">
                <div class="crypto-name">Binance Coin (BNB)</div>
                <div class="crypto-price">$315.50</div>
                <div class="crypto-change positive">+0.89%</div>
            </div>
            
            <div class="crypto-item">
                <div class="crypto-name">Cardano (ADA)</div>
                <div class="crypto-price">$0.4850</div>
                <div class="crypto-change positive">+3.45%</div>
            </div>
            
            <div class="crypto-item">
                <div class="crypto-name">Solana (SOL)</div>
                <div class="crypto-price">$98.75</div>
                <div class="crypto-change negative">-2.11%</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔍 其他测试元素</h3>
            <p>这些元素包含价格信息，应该被自动识别和更新：</p>
            
            <div style="margin: 10px 0;">
                <strong>Bitcoin 当前价格：</strong> $43,250.00
            </div>
            
            <div style="margin: 10px 0;">
                <strong>ETH 交易价格：</strong> $2,650.00
            </div>
            
            <div style="margin: 10px 0;">
                <strong>BNB 市场价：</strong> $315.50
            </div>
            
            <div style="margin: 10px 0;">
                <strong>Dogecoin 价格：</strong> $0.0850
            </div>
        </div>
        
        <div class="test-section">
            <h3>📈 使用说明</h3>
            <ul>
                <li><strong>自动更新：</strong> 价格每2秒自动更新一次</li>
                <li><strong>状态指示器：</strong> 右上角显示实时状态</li>
                <li><strong>手动刷新：</strong> 点击🔄按钮或使用 Ctrl+Shift+R</li>
                <li><strong>动画效果：</strong> 价格变化时会有颜色动画</li>
                <li><strong>智能识别：</strong> 自动识别页面中的价格元素</li>
            </ul>
        </div>
        
        <div id="dynamicElements" class="test-section" style="display: none;">
            <h3>🆕 动态添加的元素</h3>
            <div id="newElementsContainer"></div>
        </div>
    </div>

    <script>
        function manualRefresh() {
            if (window.simplePriceUpdater) {
                window.simplePriceUpdater.manualRefresh();
                showNotification('🔄 价格已手动刷新');
            } else {
                showNotification('❌ 价格更新器未启动');
            }
        }
        
        function addMoreElements() {
            const container = document.getElementById('newElementsContainer');
            const dynamicSection = document.getElementById('dynamicElements');
            
            const newElements = [
                { name: 'Polkadot (DOT)', price: '$7.25', symbol: 'DOT' },
                { name: 'Chainlink (LINK)', price: '$14.25', symbol: 'LINK' },
                { name: 'Litecoin (LTC)', price: '$72.50', symbol: 'LTC' },
                { name: 'VeChain (VET)', price: '$0.0320', symbol: 'VET' }
            ];
            
            newElements.forEach(coin => {
                const element = document.createElement('div');
                element.className = 'crypto-item';
                element.innerHTML = `
                    <div class="crypto-name">${coin.name}</div>
                    <div class="crypto-price">${coin.price}</div>
                    <div class="crypto-change positive">+0.00%</div>
                `;
                container.appendChild(element);
            });
            
            dynamicSection.style.display = 'block';
            
            // 重新扫描元素
            if (window.simplePriceUpdater) {
                setTimeout(() => {
                    window.simplePriceUpdater.scanForPriceElements();
                }, 100);
            }
            
            showNotification('➕ 已添加新的价格元素');
        }
        
        function showSystemInfo() {
            if (window.simplePriceUpdater) {
                window.simplePriceUpdater.showStatus();
            } else {
                showNotification('❌ 价格更新器未启动');
            }
        }
        
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: #1f2937;
                color: white;
                padding: 12px 24px;
                border-radius: 8px;
                z-index: 10000;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                animation: slideDown 0.3s ease;
            `;
            
            notification.textContent = message;
            document.body.appendChild(notification);
            
            // 添加动画样式
            if (!document.getElementById('notificationStyle')) {
                const style = document.createElement('style');
                style.id = 'notificationStyle';
                style.textContent = `
                    @keyframes slideDown {
                        from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                        to { transform: translateX(-50%) translateY(0); opacity: 1; }
                    }
                `;
                document.head.appendChild(style);
            }
            
            setTimeout(() => {
                notification.style.animation = 'slideDown 0.3s ease reverse';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
        
        // 页面加载完成后显示欢迎信息
        window.addEventListener('load', () => {
            setTimeout(() => {
                showNotification('🎉 价格更新系统已启动！');
            }, 3000);
        });
        
        // 监听价格更新事件
        document.addEventListener('priceUpdated', (e) => {
            console.log('价格更新事件:', e.detail);
        });
    </script>
    
    <!-- 引入简单价格更新器 -->
    <script src="simple-price-updater.js"></script>
</body>
</html>
