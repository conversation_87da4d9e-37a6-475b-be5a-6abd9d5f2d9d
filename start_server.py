#!/usr/bin/env python3
"""
快速启动脚本 - 自动安装依赖并启动价格服务器
"""

import subprocess
import sys
import os
import time

def install_requirements():
    """安装必要的依赖"""
    print("📦 检查并安装依赖...")
    
    try:
        import aiohttp
        print("✅ aiohttp 已安装")
    except ImportError:
        print("📥 正在安装 aiohttp...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "aiohttp"])
        print("✅ aiohttp 安装完成")

def start_server():
    """启动价格服务器"""
    print("🚀 启动加密货币价格服务器...")

    try:
        # 启动服务器
        subprocess.run([sys.executable, "crypto_price_server.py"])
    except KeyboardInterrupt:
        print("\n⏹️  服务器已停止")
    except Exception as e:
        print(f"❌ 启动服务器时出错: {e}")
        return False

    return True

def main():
    print("=" * 50)
    print("🚀 加密货币实时价格系统启动器")
    print("=" * 50)
    
    # 检查文件是否存在
    if not os.path.exists("crypto_price_server.py"):
        print("❌ 找不到 crypto_price_server.py 文件")
        print("请确保所有文件都在同一目录下")
        return
    
    # 安装依赖
    install_requirements()
    
    print("\n📋 启动信息:")
    print("- 服务器地址: http://localhost:8080")
    print("- API接口: http://localhost:8080/api/prices")
    print("- 健康检查: http://localhost:8080/health")
    print("- 按 Ctrl+C 停止服务器")
    print("\n" + "=" * 50)
    
    # 启动服务器
    start_server()

if __name__ == "__main__":
    main()
