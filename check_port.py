#!/usr/bin/env python3
"""
端口检查工具 - 查找占用8080端口的程序
"""

import subprocess
import sys
import platform

def check_port_usage():
    """检查端口8080的使用情况"""
    print("🔍 检查端口8080的使用情况...")
    
    system = platform.system().lower()
    
    try:
        if system == "darwin" or system == "linux":  # macOS or Linux
            # 使用 lsof 命令
            result = subprocess.run(['lsof', '-i', ':8080'], 
                                  capture_output=True, text=True)
            if result.returncode == 0 and result.stdout.strip():
                print("📋 端口8080被以下程序占用:")
                print(result.stdout)
                
                # 提取PID
                lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
                pids = []
                for line in lines:
                    parts = line.split()
                    if len(parts) >= 2:
                        pids.append(parts[1])
                
                if pids:
                    print(f"\n💡 要关闭这些程序，可以运行:")
                    for pid in set(pids):  # 去重
                        print(f"   kill {pid}")
                    
                    print(f"\n⚠️  或者强制关闭:")
                    for pid in set(pids):
                        print(f"   kill -9 {pid}")
                
            else:
                print("✅ 端口8080未被占用")
                
        elif system == "windows":
            # 使用 netstat 命令
            result = subprocess.run(['netstat', '-ano'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                port_8080_lines = [line for line in lines if ':8080' in line and 'LISTENING' in line]
                
                if port_8080_lines:
                    print("📋 端口8080被以下程序占用:")
                    for line in port_8080_lines:
                        print(line.strip())
                        parts = line.strip().split()
                        if len(parts) >= 5:
                            pid = parts[-1]
                            print(f"💡 要关闭PID {pid}，可以运行: taskkill /PID {pid} /F")
                else:
                    print("✅ 端口8080未被占用")
            else:
                print("❌ 无法检查端口状态")
        
    except FileNotFoundError:
        print("❌ 缺少必要的系统命令")
        print("💡 请手动检查端口占用情况")
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")

def kill_port_8080():
    """尝试自动关闭占用8080端口的程序"""
    print("\n🔧 尝试自动关闭占用8080端口的程序...")
    
    system = platform.system().lower()
    
    try:
        if system == "darwin" or system == "linux":
            # 查找PID
            result = subprocess.run(['lsof', '-t', '-i', ':8080'], 
                                  capture_output=True, text=True)
            if result.returncode == 0 and result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    if pid.strip():
                        print(f"🔪 关闭进程 PID: {pid}")
                        subprocess.run(['kill', pid.strip()])
                
                print("✅ 已尝试关闭占用端口的程序")
                return True
            else:
                print("✅ 没有找到占用端口的程序")
                return True
                
        elif system == "windows":
            # Windows下查找并关闭
            result = subprocess.run(['netstat', '-ano'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if ':8080' in line and 'LISTENING' in line:
                        parts = line.strip().split()
                        if len(parts) >= 5:
                            pid = parts[-1]
                            print(f"🔪 关闭进程 PID: {pid}")
                            subprocess.run(['taskkill', '/PID', pid, '/F'])
                
                print("✅ 已尝试关闭占用端口的程序")
                return True
        
    except Exception as e:
        print(f"❌ 自动关闭失败: {e}")
        return False
    
    return False

def main():
    print("=" * 60)
    print("🔍 端口8080检查工具")
    print("=" * 60)
    
    # 检查端口使用情况
    check_port_usage()
    
    # 询问是否自动关闭
    print("\n" + "=" * 60)
    choice = input("🤔 是否尝试自动关闭占用端口的程序? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', '是']:
        success = kill_port_8080()
        if success:
            print("\n🎉 端口清理完成！")
            print("💡 现在可以重新运行: python start_server.py")
        else:
            print("\n❌ 自动清理失败，请手动关闭程序")
    else:
        print("\n💡 请手动关闭占用端口的程序，然后重新启动服务器")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
