#!/usr/bin/env python3
"""
加密货币价格服务器
获取真实的加密货币价格数据并提供API接口
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Any
import aiohttp
from aiohttp import web, ClientSession
from aiohttp.web import middleware
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CryptoPriceServer:
    def __init__(self):
        self.price_data = {}
        self.last_update = 0
        self.update_interval = 10  # 10秒更新一次
        self.session = None
        
        # 支持的加密货币映射
        self.coin_mapping = {
            'BTC': 'bitcoin',
            'ETH': 'ethereum', 
            'BNB': 'binancecoin',
            'ADA': 'cardano',
            'SOL': 'solana',
            'DOT': 'polkadot',
            'DOGE': 'dogecoin',
            'AVAX': 'avalanche-2',
            'MATIC': 'matic-network',
            'LINK': 'chainlink',
            'LTC': 'litecoin',
            'BCH': 'bitcoin-cash',
            'XLM': 'stellar',
            'VET': 'vechain',
            'FIL': 'filecoin'
        }

    async def init_session(self):
        """初始化HTTP会话"""
        if not self.session:
            self.session = ClientSession()

    async def close_session(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()

    async def fetch_prices_coingecko(self) -> Dict[str, Any]:
        """从CoinGecko获取价格数据"""
        try:
            coin_ids = ','.join(self.coin_mapping.values())
            url = f"https://api.coingecko.com/api/v3/simple/price"
            params = {
                'ids': coin_ids,
                'vs_currencies': 'usd',
                'include_24hr_change': 'true',
                'include_last_updated_at': 'true'
            }
            
            async with self.session.get(url, params=params, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 转换数据格式
                    result = {}
                    for symbol, coin_id in self.coin_mapping.items():
                        if coin_id in data:
                            coin_data = data[coin_id]
                            result[symbol] = {
                                'price': coin_data.get('usd', 0),
                                'change_24h': coin_data.get('usd_24h_change', 0),
                                'last_updated': coin_data.get('last_updated_at', int(time.time())),
                                'source': 'coingecko'
                            }
                    
                    logger.info(f"CoinGecko: 获取到 {len(result)} 个币种数据")
                    return result
                else:
                    logger.warning(f"CoinGecko API 错误: {response.status}")
                    return {}
                    
        except Exception as e:
            logger.error(f"CoinGecko API 请求失败: {e}")
            return {}

    async def fetch_prices_coinbase(self) -> Dict[str, Any]:
        """从Coinbase获取价格数据"""
        try:
            result = {}
            # Coinbase API 需要逐个获取
            for symbol in ['BTC', 'ETH', 'BNB', 'ADA', 'SOL']:
                try:
                    url = f"https://api.coinbase.com/v2/exchange-rates?currency={symbol}"
                    async with self.session.get(url, timeout=5) as response:
                        if response.status == 200:
                            data = await response.json()
                            if 'data' in data and 'rates' in data['data']:
                                usd_rate = float(data['data']['rates'].get('USD', 0))
                                if usd_rate > 0:
                                    result[symbol] = {
                                        'price': usd_rate,
                                        'change_24h': 0,  # Coinbase API 不提供24h变化
                                        'last_updated': int(time.time()),
                                        'source': 'coinbase'
                                    }
                except Exception as e:
                    logger.warning(f"获取 {symbol} 价格失败: {e}")
                    continue
            
            logger.info(f"Coinbase: 获取到 {len(result)} 个币种数据")
            return result
            
        except Exception as e:
            logger.error(f"Coinbase API 请求失败: {e}")
            return {}

    async def fetch_prices_binance(self) -> Dict[str, Any]:
        """从Binance获取价格数据"""
        try:
            url = "https://api.binance.com/api/v3/ticker/24hr"
            async with self.session.get(url, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    result = {}
                    for item in data:
                        symbol = item['symbol']
                        if symbol.endswith('USDT'):
                            base_symbol = symbol.replace('USDT', '')
                            if base_symbol in self.coin_mapping:
                                result[base_symbol] = {
                                    'price': float(item['lastPrice']),
                                    'change_24h': float(item['priceChangePercent']),
                                    'last_updated': int(time.time()),
                                    'source': 'binance'
                                }
                    
                    logger.info(f"Binance: 获取到 {len(result)} 个币种数据")
                    return result
                else:
                    logger.warning(f"Binance API 错误: {response.status}")
                    return {}
                    
        except Exception as e:
            logger.error(f"Binance API 请求失败: {e}")
            return {}

    async def update_prices(self):
        """更新价格数据"""
        logger.info("开始更新价格数据...")
        
        # 尝试多个数据源
        sources = [
            self.fetch_prices_coingecko,
            self.fetch_prices_binance,
            self.fetch_prices_coinbase
        ]
        
        for fetch_func in sources:
            try:
                new_data = await fetch_func()
                if new_data:
                    self.price_data.update(new_data)
                    self.last_update = time.time()
                    logger.info(f"价格数据更新成功，共 {len(self.price_data)} 个币种")
                    break
            except Exception as e:
                logger.error(f"数据源更新失败: {e}")
                continue
        
        if not self.price_data:
            logger.warning("所有数据源都失败，使用备用数据")
            await self.use_fallback_data()

    async def use_fallback_data(self):
        """使用备用数据（基于历史价格的估算）"""
        fallback_prices = {
            'BTC': 43250.00,
            'ETH': 2650.00,
            'BNB': 315.50,
            'ADA': 0.485,
            'SOL': 98.75,
            'DOT': 7.25,
            'DOGE': 0.085,
            'AVAX': 36.80,
            'MATIC': 0.875,
            'LINK': 14.25,
            'LTC': 72.50,
            'BCH': 245.00,
            'XLM': 0.125,
            'VET': 0.032,
            'FIL': 5.85
        }
        
        for symbol, price in fallback_prices.items():
            self.price_data[symbol] = {
                'price': price,
                'change_24h': 0,
                'last_updated': int(time.time()),
                'source': 'fallback'
            }
        
        logger.info("已加载备用价格数据")

    async def price_update_loop(self):
        """价格更新循环"""
        while True:
            try:
                await self.update_prices()
                await asyncio.sleep(self.update_interval)
            except Exception as e:
                logger.error(f"价格更新循环错误: {e}")
                await asyncio.sleep(5)

# CORS中间件
@middleware
async def cors_handler(request, handler):
    response = await handler(request)
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
    return response

# 创建服务器实例
server = CryptoPriceServer()

async def get_prices(request):
    """获取价格数据的API端点"""
    try:
        # 如果数据太旧，强制更新
        if time.time() - server.last_update > 60:
            await server.update_prices()
        
        response_data = {
            'success': True,
            'data': server.price_data,
            'last_update': server.last_update,
            'timestamp': time.time(),
            'count': len(server.price_data)
        }
        
        return web.json_response(response_data)
        
    except Exception as e:
        logger.error(f"API请求处理错误: {e}")
        return web.json_response({
            'success': False,
            'error': str(e),
            'timestamp': time.time()
        }, status=500)

async def health_check(request):
    """健康检查端点"""
    return web.json_response({
        'status': 'healthy',
        'last_update': server.last_update,
        'data_count': len(server.price_data),
        'timestamp': time.time()
    })

async def init_app():
    """初始化应用"""
    app = web.Application(middlewares=[cors_handler])
    
    # 添加路由
    app.router.add_get('/api/prices', get_prices)
    app.router.add_get('/health', health_check)
    app.router.add_options('/api/prices', lambda r: web.Response())
    
    # 初始化服务器
    await server.init_session()
    
    # 启动价格更新循环
    asyncio.create_task(server.price_update_loop())
    
    # 初始更新
    await server.update_prices()
    
    return app

async def cleanup(app):
    """清理资源"""
    await server.close_session()

if __name__ == '__main__':
    # 创建应用
    app = init_app()
    
    # 设置清理函数
    app.on_cleanup.append(cleanup)
    
    # 启动服务器
    print("🚀 启动加密货币价格服务器...")
    print("📊 API地址: http://localhost:8080/api/prices")
    print("🏥 健康检查: http://localhost:8080/health")
    
    web.run_app(app, host='localhost', port=8080)
