<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时价格更新演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .crypto-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .crypto-card {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .crypto-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .crypto-name {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2d3748;
        }
        
        .crypto-price {
            font-size: 2em;
            font-weight: bold;
            color: #3182ce;
            margin-bottom: 5px;
        }
        
        .crypto-change {
            font-size: 1.1em;
            font-weight: 500;
        }
        
        .positive { color: #38a169; }
        .negative { color: #e53e3e; }
        
        .info-section {
            background: #f7fafc;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .feature-list li:before {
            content: "✅ ";
            margin-right: 10px;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .crypto-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 实时加密货币价格演示</h1>
        
        <div class="crypto-grid">
            <div class="crypto-card">
                <div class="crypto-name">Bitcoin (BTC)</div>
                <div class="crypto-price">$45,234.56</div>
                <div class="crypto-change positive">+2.34%</div>
            </div>
            
            <div class="crypto-card">
                <div class="crypto-name">Ethereum (ETH)</div>
                <div class="crypto-price">$3,123.45</div>
                <div class="crypto-change negative">-1.23%</div>
            </div>
            
            <div class="crypto-card">
                <div class="crypto-name">Binance Coin (BNB)</div>
                <div class="crypto-price">$234.56</div>
                <div class="crypto-change positive">+0.89%</div>
            </div>
            
            <div class="crypto-card">
                <div class="crypto-name">Cardano (ADA)</div>
                <div class="crypto-price">$0.4567</div>
                <div class="crypto-change positive">+3.45%</div>
            </div>
            
            <div class="crypto-card">
                <div class="crypto-name">Solana (SOL)</div>
                <div class="crypto-price">$89.12</div>
                <div class="crypto-change negative">-2.11%</div>
            </div>
            
            <div class="crypto-card">
                <div class="crypto-name">Dogecoin (DOGE)</div>
                <div class="crypto-price">$0.0789</div>
                <div class="crypto-change positive">+5.67%</div>
            </div>
        </div>
        
        <div class="info-section">
            <h2>🎯 实时价格更新功能特点</h2>
            <ul class="feature-list">
                <li>WebSocket实时数据流 (Binance API)</li>
                <li>多API源自动切换 (CoinGecko, CoinCap, CoinLore)</li>
                <li>智能价格元素识别</li>
                <li>实时状态指示器</li>
                <li>手动刷新功能</li>
                <li>价格变化动画效果</li>
                <li>24小时变化百分比显示</li>
                <li>自动重连机制</li>
                <li>性能优化和错误处理</li>
                <li>移动端适配</li>
            </ul>
            
            <h3>🎮 使用说明</h3>
            <p><strong>状态指示器：</strong> 右上角显示实时更新状态</p>
            <p><strong>手动刷新：</strong> 点击状态指示器中的🔄按钮或使用 Ctrl+Shift+R</p>
            <p><strong>详细信息：</strong> 点击状态指示器查看详细状态</p>
            <p><strong>更新频率：</strong> WebSocket实时更新，API备用每3秒</p>
        </div>
    </div>

    <!-- 引入实时价格更新系统 -->
    <script>
        // 这里会包含完整的CryptoPriceUpdater类
        // 为了演示，我们创建一个简化版本
        
        class DemoCryptoPriceUpdater {
            constructor() {
                this.updateInterval = 3000;
                this.priceData = {};
                this.priceElements = new Map();
                this.lastUpdateTime = 0;
                this.updateCounter = 0;
                this.isUpdating = false;
                this.init();
            }
            
            async init() {
                console.log('🚀 启动演示价格更新系统...');
                this.createStatusIndicator();
                this.scanForPriceElements();
                this.startMockUpdates();
                console.log('✅ 演示系统已启动');
            }
            
            createStatusIndicator() {
                const indicator = document.createElement('div');
                indicator.id = 'priceUpdateIndicator';
                indicator.style.cssText = `
                    position: fixed;
                    top: 10px;
                    right: 10px;
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 8px 12px;
                    border-radius: 20px;
                    font-size: 12px;
                    z-index: 10000;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    transition: all 0.3s ease;
                    cursor: pointer;
                `;
                
                indicator.innerHTML = `
                    <div id="statusDot" style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></div>
                    <span id="statusText">演示模式</span>
                    <span id="updateCounter">0</span>
                `;
                
                document.body.appendChild(indicator);
            }
            
            scanForPriceElements() {
                const priceElements = document.querySelectorAll('.crypto-price');
                priceElements.forEach(element => {
                    const card = element.closest('.crypto-card');
                    const nameElement = card.querySelector('.crypto-name');
                    const coinName = nameElement.textContent;
                    
                    let coinType = 'BTC';
                    if (coinName.includes('Ethereum')) coinType = 'ETH';
                    else if (coinName.includes('Binance')) coinType = 'BNB';
                    else if (coinName.includes('Cardano')) coinType = 'ADA';
                    else if (coinName.includes('Solana')) coinType = 'SOL';
                    else if (coinName.includes('Dogecoin')) coinType = 'DOGE';
                    
                    this.priceElements.set(element, {
                        coinType: coinType,
                        originalText: element.textContent,
                        priceRegex: /\$[\d,]+\.?\d*/g
                    });
                });
                
                console.log(`找到 ${this.priceElements.size} 个价格元素`);
            }
            
            startMockUpdates() {
                setInterval(() => {
                    this.updateCounter++;
                    document.getElementById('updateCounter').textContent = this.updateCounter;
                    
                    // 模拟价格变化
                    this.priceElements.forEach((info, element) => {
                        const currentPrice = parseFloat(element.textContent.replace('$', '').replace(',', ''));
                        const change = (Math.random() - 0.5) * 0.02; // ±1%的随机变化
                        const newPrice = currentPrice * (1 + change);
                        
                        element.textContent = this.formatPrice(newPrice);
                        
                        // 添加动画效果
                        element.style.transition = 'all 0.3s ease';
                        element.style.backgroundColor = change > 0 ? '#dcfce7' : '#fef2f2';
                        element.style.transform = 'scale(1.02)';
                        
                        setTimeout(() => {
                            element.style.backgroundColor = '';
                            element.style.transform = 'scale(1)';
                        }, 500);
                    });
                }, this.updateInterval);
            }
            
            formatPrice(price) {
                if (price >= 1) {
                    return `$${price.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
                } else {
                    return `$${price.toFixed(4)}`;
                }
            }
        }
        
        // 启动演示系统
        setTimeout(() => {
            window.demoPriceUpdater = new DemoCryptoPriceUpdater();
        }, 1000);
    </script>
</body>
</html>
