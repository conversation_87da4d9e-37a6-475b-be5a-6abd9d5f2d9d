#!/usr/bin/env python3
"""
API测试脚本 - 测试价格服务器是否正常工作
"""

import asyncio
import aiohttp
import json
import time

async def test_api():
    """测试API接口"""
    print("🧪 测试加密货币价格API...")
    
    async with aiohttp.ClientSession() as session:
        try:
            # 测试健康检查
            print("\n1. 测试健康检查接口...")
            async with session.get('http://localhost:8080/health') as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 健康检查通过: {data}")
                else:
                    print(f"❌ 健康检查失败: {response.status}")
                    return False
            
            # 测试价格接口
            print("\n2. 测试价格数据接口...")
            async with session.get('http://localhost:8080/api/prices') as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if data.get('success'):
                        print(f"✅ 价格数据获取成功")
                        print(f"📊 币种数量: {data.get('count', 0)}")
                        print(f"🕐 最后更新: {time.ctime(data.get('last_update', 0))}")
                        
                        # 显示前5个币种的价格
                        print("\n💰 价格示例:")
                        count = 0
                        for symbol, info in data.get('data', {}).items():
                            if count >= 5:
                                break
                            price = info.get('price', 0)
                            change = info.get('change_24h', 0)
                            source = info.get('source', 'unknown')
                            
                            change_str = f"+{change:.2f}%" if change > 0 else f"{change:.2f}%"
                            print(f"  {symbol}: ${price:,.4f} ({change_str}) [{source}]")
                            count += 1
                        
                        return True
                    else:
                        print(f"❌ API返回错误: {data}")
                        return False
                else:
                    print(f"❌ 价格接口失败: {response.status}")
                    return False
                    
        except aiohttp.ClientConnectorError:
            print("❌ 无法连接到服务器")
            print("💡 请确保已启动价格服务器: python crypto_price_server.py")
            return False
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            return False

def main():
    print("=" * 60)
    print("🧪 加密货币价格API测试工具")
    print("=" * 60)
    
    # 运行异步测试
    result = asyncio.run(test_api())
    
    print("\n" + "=" * 60)
    if result:
        print("🎉 所有测试通过！API工作正常")
        print("💡 现在可以打开 explore.html 查看实时价格更新")
    else:
        print("❌ 测试失败，请检查服务器状态")
        print("🔧 故障排除:")
        print("   1. 确认已启动服务器: python crypto_price_server.py")
        print("   2. 检查端口8080是否被占用")
        print("   3. 检查网络连接")
    print("=" * 60)

if __name__ == "__main__":
    main()
