#!/usr/bin/env python3
"""
前端端口更新工具 - 自动更新explore.html中的API端口
"""

import re
import os

def find_server_port():
    """查找服务器实际使用的端口"""
    import socket
    
    # 检查常用端口
    ports_to_check = [8080, 8081, 8082, 8083, 8084, 8085]
    
    for port in ports_to_check:
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                result = s.connect_ex(('localhost', port))
                if result == 0:
                    # 端口被占用，可能是我们的服务器
                    return port
        except:
            continue
    
    return 8080  # 默认端口

def update_frontend_port(port):
    """更新explore.html中的API端口"""
    
    if not os.path.exists('explore.html'):
        print("❌ 找不到 explore.html 文件")
        return False
    
    try:
        # 读取文件
        with open('explore.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换API URL
        old_pattern = r'this\.apiUrl = [\'"]http://localhost:\d+/api/prices[\'"]'
        new_url = f'this.apiUrl = "http://localhost:{port}/api/prices"'
        
        if re.search(old_pattern, content):
            content = re.sub(old_pattern, new_url, content)
            
            # 写回文件
            with open('explore.html', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 已更新 explore.html 中的API地址为: http://localhost:{port}/api/prices")
            return True
        else:
            print("⚠️  在 explore.html 中未找到API URL配置")
            return False
            
    except Exception as e:
        print(f"❌ 更新文件时出错: {e}")
        return False

def main():
    print("🔧 前端端口配置更新工具")
    print("=" * 50)
    
    # 让用户选择端口
    print("请选择操作:")
    print("1. 自动检测服务器端口")
    print("2. 手动指定端口")
    print("3. 重置为默认端口 (8080)")
    
    choice = input("\n请输入选择 (1-3): ").strip()
    
    if choice == "1":
        port = find_server_port()
        print(f"🔍 检测到服务器可能运行在端口: {port}")
    elif choice == "2":
        try:
            port = int(input("请输入端口号: ").strip())
            if port < 1024 or port > 65535:
                print("❌ 端口号必须在 1024-65535 范围内")
                return
        except ValueError:
            print("❌ 请输入有效的端口号")
            return
    elif choice == "3":
        port = 8080
        print("🔄 重置为默认端口: 8080")
    else:
        print("❌ 无效选择")
        return
    
    # 更新前端配置
    success = update_frontend_port(port)
    
    if success:
        print(f"\n🎉 配置更新完成！")
        print(f"📋 API地址: http://localhost:{port}/api/prices")
        print(f"💡 现在可以打开 explore.html 查看实时价格更新")
    else:
        print(f"\n❌ 配置更新失败")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
