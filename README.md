# 🚀 实时加密货币价格更新系统

这是一个完整的实时加密货币价格更新系统，包含Python后端和前端页面，提供真实的市场数据。

## 📋 系统特点

### ✅ 真实数据源
- **CoinGecko API** - 主要数据源
- **Binance API** - 备用数据源  
- **Coinbase API** - 第三备用源
- **自动切换** - 数据源失败时自动切换

### ✅ 可靠性保证
- **多重备份** - 多个API源确保数据可用性
- **自动重试** - 网络失败时自动重试
- **错误处理** - 完善的错误处理机制
- **状态监控** - 实时显示连接状态

### ✅ 用户体验
- **实时更新** - 每5秒更新一次价格
- **动画效果** - 价格变化时的视觉反馈
- **状态指示器** - 右上角实时状态显示
- **手动刷新** - 支持手动刷新和快捷键

## 🛠️ 快速开始

### 方法1：使用启动脚本（推荐）

```bash
# 1. 运行启动脚本（自动安装依赖）
python start_server.py
```

### 方法2：手动启动

```bash
# 1. 安装依赖
pip install aiohttp

# 2. 启动服务器
python crypto_price_server.py
```

### 3. 打开前端页面

启动服务器后，打开 `explore.html` 页面即可看到实时价格更新。

## 📊 支持的加密货币

| 符号 | 名称 | 符号 | 名称 |
|------|------|------|------|
| BTC | Bitcoin | ETH | Ethereum |
| BNB | Binance Coin | ADA | Cardano |
| SOL | Solana | DOT | Polkadot |
| DOGE | Dogecoin | AVAX | Avalanche |
| MATIC | Polygon | LINK | Chainlink |
| LTC | Litecoin | BCH | Bitcoin Cash |
| XLM | Stellar | VET | VeChain |
| FIL | Filecoin | | |

## 🔧 API接口

### 获取价格数据
```
GET http://localhost:8080/api/prices
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "BTC": {
      "price": 43250.00,
      "change_24h": 2.34,
      "last_updated": **********,
      "source": "coingecko"
    },
    "ETH": {
      "price": 2650.00,
      "change_24h": -1.23,
      "last_updated": **********,
      "source": "coingecko"
    }
  },
  "last_update": **********,
  "timestamp": **********.789,
  "count": 15
}
```

### 健康检查
```
GET http://localhost:8080/health
```

## 🎮 使用说明

### 状态指示器
右上角的状态指示器显示系统状态：
- 🟡 **黄点** - 连接中
- 🟢 **绿点** - 连接成功，数据正常
- 🔵 **蓝点** - 正在更新数据
- 🔴 **红点** - 连接失败或错误

### 手动操作
- **手动刷新**: 点击🔄按钮
- **快捷键**: `Ctrl+Shift+R` (Mac: `Cmd+Shift+R`)
- **查看详情**: 点击状态指示器主体

### 自动功能
- **智能识别**: 自动扫描页面中的价格元素
- **实时更新**: 每5秒自动更新价格
- **动画效果**: 价格变化时的颜色动画
- **错误恢复**: 网络错误时自动重试

## 🔍 故障排除

### 问题1：后端无法启动
```bash
# 检查Python版本（需要3.7+）
python --version

# 重新安装依赖
pip install --upgrade aiohttp
```

### 问题2：端口被占用
```bash
# 查看端口占用
netstat -an | grep 8080

# 或者修改端口（在crypto_price_server.py最后一行）
web.run_app(app, host='localhost', port=8081)
```

### 问题3：前端无法连接后端
1. 确认后端已启动并显示"启动加密货币价格服务器..."
2. 访问 http://localhost:8080/health 检查服务状态
3. 检查浏览器控制台是否有CORS错误

### 问题4：数据获取失败
- 检查网络连接
- 查看后端日志中的错误信息
- API可能有频率限制，稍等片刻再试

## 📁 文件说明

| 文件 | 说明 |
|------|------|
| `crypto_price_server.py` | Python后端服务器 |
| `start_server.py` | 快速启动脚本 |
| `explore.html` | 主前端页面（已集成真实数据） |
| `test-price-update.html` | 测试页面 |
| `requirements.txt` | Python依赖列表 |
| `README.md` | 说明文档 |

## 🚀 高级配置

### 修改更新频率
在 `explore.html` 中修改：
```javascript
this.updateInterval = 5000; // 5秒，可改为其他值
```

### 添加新的加密货币
在 `crypto_price_server.py` 中的 `coin_mapping` 添加：
```python
self.coin_mapping = {
    # 现有币种...
    'NEW': 'new-coin-id',  # 添加新币种
}
```

### 修改服务器端口
在 `crypto_price_server.py` 最后一行：
```python
web.run_app(app, host='localhost', port=8080)  # 修改端口号
```

## 📞 技术支持

如果遇到问题：
1. 查看浏览器控制台的错误信息
2. 查看Python后端的日志输出
3. 确认网络连接正常
4. 检查防火墙设置

## 🎯 下一步计划

- [ ] 添加更多加密货币支持
- [ ] 实现价格警报功能
- [ ] 添加历史价格图表
- [ ] 支持多种法币显示
- [ ] 移动端优化

---

**享受实时加密货币价格更新！** 🚀📈
