#!/usr/bin/env python3
"""
一体化启动脚本 - 自动处理端口冲突并启动服务
"""

import subprocess
import sys
import os
import time
import socket
import re
import threading
import signal

class CryptoServerManager:
    def __init__(self):
        self.server_process = None
        self.server_port = None
        
    def find_available_port(self, start_port=8080, max_attempts=10):
        """查找可用端口"""
        for port in range(start_port, start_port + max_attempts):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return port
            except OSError:
                continue
        return None
    
    def check_port_occupied(self, port):
        """检查端口是否被占用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                result = s.connect_ex(('localhost', port))
                return result == 0
        except:
            return False
    
    def kill_port_process(self, port):
        """尝试关闭占用指定端口的进程"""
        try:
            import platform
            system = platform.system().lower()
            
            if system == "darwin" or system == "linux":
                # 查找并关闭进程
                result = subprocess.run(['lsof', '-t', '-i', f':{port}'], 
                                      capture_output=True, text=True)
                if result.returncode == 0 and result.stdout.strip():
                    pids = result.stdout.strip().split('\n')
                    for pid in pids:
                        if pid.strip():
                            print(f"🔪 关闭占用端口{port}的进程 PID: {pid}")
                            subprocess.run(['kill', pid.strip()])
                    return True
            elif system == "windows":
                # Windows处理
                result = subprocess.run(['netstat', '-ano'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if f':{port}' in line and 'LISTENING' in line:
                            parts = line.strip().split()
                            if len(parts) >= 5:
                                pid = parts[-1]
                                print(f"🔪 关闭占用端口{port}的进程 PID: {pid}")
                                subprocess.run(['taskkill', '/PID', pid, '/F'])
                    return True
        except Exception as e:
            print(f"❌ 关闭进程失败: {e}")
            return False
        
        return False
    
    def update_frontend_port(self, port):
        """更新explore.html中的API端口"""
        if not os.path.exists('explore.html'):
            print("❌ 找不到 explore.html 文件")
            return False
        
        try:
            with open('explore.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换API URL
            old_pattern = r'this\.apiUrl = [\'"]http://localhost:\d+/api/prices[\'"]'
            new_url = f'this.apiUrl = "http://localhost:{port}/api/prices"'
            
            if re.search(old_pattern, content):
                content = re.sub(old_pattern, new_url, content)
                
                with open('explore.html', 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ 已更新前端API地址为: http://localhost:{port}/api/prices")
                return True
            else:
                print("⚠️  未找到API URL配置，可能需要手动更新")
                return False
                
        except Exception as e:
            print(f"❌ 更新前端配置失败: {e}")
            return False
    
    def start_server(self, port):
        """启动价格服务器"""
        try:
            # 修改服务器文件中的端口
            self.modify_server_port(port)
            
            print(f"🚀 在端口 {port} 启动价格服务器...")
            
            # 启动服务器进程
            self.server_process = subprocess.Popen(
                [sys.executable, 'crypto_price_server.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.server_port = port
            
            # 等待服务器启动
            time.sleep(3)
            
            # 检查服务器是否成功启动
            if self.check_server_health(port):
                print(f"✅ 服务器成功启动在端口 {port}")
                return True
            else:
                print(f"❌ 服务器启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动服务器时出错: {e}")
            return False
    
    def modify_server_port(self, port):
        """临时修改服务器端口配置"""
        # 这里我们使用环境变量来传递端口
        os.environ['CRYPTO_SERVER_PORT'] = str(port)
    
    def check_server_health(self, port):
        """检查服务器健康状态"""
        try:
            import urllib.request
            import json
            
            url = f"http://localhost:{port}/health"
            
            for attempt in range(5):  # 尝试5次
                try:
                    with urllib.request.urlopen(url, timeout=5) as response:
                        if response.status == 200:
                            data = json.loads(response.read().decode())
                            if data.get('status') == 'healthy':
                                return True
                except:
                    time.sleep(1)
                    continue
            
            return False
            
        except Exception as e:
            print(f"健康检查失败: {e}")
            return False
    
    def stop_server(self):
        """停止服务器"""
        if self.server_process:
            print("⏹️  正在停止服务器...")
            self.server_process.terminate()
            try:
                self.server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.server_process.kill()
            print("✅ 服务器已停止")
    
    def run(self):
        """主运行流程"""
        print("=" * 60)
        print("🚀 加密货币实时价格系统 - 智能启动器")
        print("=" * 60)
        
        # 检查必要文件
        required_files = ['crypto_price_server.py', 'explore.html']
        for file in required_files:
            if not os.path.exists(file):
                print(f"❌ 找不到必要文件: {file}")
                return False
        
        # 安装依赖
        try:
            import aiohttp
            print("✅ 依赖检查通过")
        except ImportError:
            print("📦 正在安装依赖...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "aiohttp"])
        
        # 处理端口问题
        preferred_port = 8080
        
        if self.check_port_occupied(preferred_port):
            print(f"⚠️  端口 {preferred_port} 被占用")
            
            choice = input("选择处理方式:\n1. 尝试关闭占用进程\n2. 使用其他端口\n请输入选择 (1-2): ").strip()
            
            if choice == "1":
                if self.kill_port_process(preferred_port):
                    print("✅ 成功关闭占用进程")
                    time.sleep(2)
                    if not self.check_port_occupied(preferred_port):
                        port = preferred_port
                    else:
                        print("❌ 端口仍被占用，使用其他端口")
                        port = self.find_available_port(8081)
                else:
                    print("❌ 无法关闭占用进程，使用其他端口")
                    port = self.find_available_port(8081)
            else:
                port = self.find_available_port(8081)
        else:
            port = preferred_port
        
        if port is None:
            print("❌ 无法找到可用端口")
            return False
        
        print(f"📡 将使用端口: {port}")
        
        # 更新前端配置
        self.update_frontend_port(port)
        
        # 启动服务器
        if self.start_server(port):
            print("\n" + "=" * 60)
            print("🎉 系统启动成功！")
            print(f"📊 API地址: http://localhost:{port}/api/prices")
            print(f"🏥 健康检查: http://localhost:{port}/health")
            print("🌐 现在可以打开 explore.html 查看实时价格更新")
            print("⌨️  按 Ctrl+C 停止服务器")
            print("=" * 60)
            
            # 保持运行
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 收到停止信号...")
                self.stop_server()
                print("👋 再见！")
        else:
            print("❌ 系统启动失败")
            return False
        
        return True

def signal_handler(signum, frame):
    """信号处理器"""
    print("\n🛑 收到停止信号，正在关闭...")
    sys.exit(0)

def main():
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    manager = CryptoServerManager()
    manager.run()

if __name__ == "__main__":
    main()
